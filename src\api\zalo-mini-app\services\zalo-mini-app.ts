/**
 * zalo-mini-app service
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";
import axios from "axios";

const zaloMiniAppService = ({ strapi }: { strapi: Core.Strapi }) => ({
  /**
   * Get Zalo Mini App settings
   */
  async getZaloSettings() {
    try {
      const settings = await strapi.db
        .query("api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app")
        .findOne();

      if (!settings) {
        throw new Error("Zalo Mini App settings not configured");
      }

      return settings;
    } catch (error) {
      console.error("Error getting Zalo settings:", error);
      throw error;
    }
  },

  /**
   * Verify Zalo access token
   */
  async verifyZaloAccessToken(accessToken: string) {
    try {
      const response = await axios.get("https://graph.zalo.me/v2.0/me", {
        headers: {
          access_token: accessToken,
        },
      });

      if (response.data && response.data.id) {
        return {
          success: true,
          data: response.data,
        };
      }

      return {
        success: false,
        error: "Invalid access token",
      };
    } catch (error) {
      console.error("Error verifying Zalo access token:", error);
      return {
        success: false,
        error: "Failed to verify access token",
      };
    }
  },

  /**
   * Register user with Zalo Mini App
   */
  async register(data: any) {
    try {
      const { accessToken, phone, name, referCode } = data;

      // Verify access token with Zalo
      const tokenVerification = await this.verifyZaloAccessToken(accessToken);
      if (!tokenVerification.success) {
        throw new Error("Invalid Zalo access token");
      }

      const zaloUserData = tokenVerification.data;
      const zaloId = zaloUserData.id;

      // Check if user already exists with this zaloId
      const existingUser = await strapi.db
        .query("plugin::users-permissions.user")
        .findOne({
          where: {
            zaloId: zaloId,
          },
        });

      if (existingUser) {
        // Generate JWT for existing user
        const jwt = strapi.plugins["users-permissions"].services.jwt.issue({
          id: existingUser.id,
        });

        return {
          success: true,
          jwt,
          user: existingUser,
          message: "User already exists, logged in successfully",
        };
      }

      // Find refer user if referCode provided
      let referUser = null;
      if (referCode) {
        referUser = await strapi.db
          .query("plugin::users-permissions.user")
          .findOne({
            where: {
              updatedReferCode: referCode,
            },
          });
      }

      // Get default role
      const defaultRole = await strapi.db
        .query("plugin::users-permissions.role")
        .findOne({
          where: {
            type: "public",
          },
        });

      if (!defaultRole) {
        throw new Error("Default role not found");
      }

      // Create new user
      const newUser = await strapi.db
        .query("plugin::users-permissions.user")
        .create({
          data: {
            username: `zalo_${zaloId}`,
            email: `${zaloId}@zalo.temp`,
            name: name || zaloUserData.name || "Zalo User",
            phone: phone || "",
            zaloId: zaloId,
            provider: "zalo",
            confirmed: true,
            blocked: false,
            role: defaultRole.id,
            balance: 0,
            fParent: referUser?.id || null,
            verified: true,
            commission: {
              totalCommission: 0,
              sharedCommission: 0,
              managerCommission: 0,
              pendingCommission: 0,
              withdrawalCommission: 0,
            },
          },
        });

      // Generate JWT
      const jwt = strapi.plugins["users-permissions"].services.jwt.issue({
        id: newUser.id,
      });

      return {
        success: true,
        jwt,
        user: newUser,
        message: "User registered successfully",
      };
    } catch (error) {
      console.error("Error in register:", error);
      throw error;
    }
  },

  /**
   * Login callback for Zalo Mini App
   */
  async callback(data: any) {
    try {
      const { accessToken, zaloId } = data;

      // Verify access token if provided
      if (accessToken) {
        const tokenVerification = await this.verifyZaloAccessToken(accessToken);
        if (!tokenVerification.success) {
          throw new Error("Invalid Zalo access token");
        }
      }

      // Find user by zaloId
      const user = await strapi.db
        .query("plugin::users-permissions.user")
        .findOne({
          where: {
            zaloId: zaloId,
          },
          populate: {
            role: true,
          },
        });

      if (!user) {
        throw new Error("User not found. Please register first.");
      }

      if (user.blocked) {
        throw new Error("Your account has been blocked");
      }

      // Generate JWT
      const jwt = strapi.plugins["users-permissions"].services.jwt.issue({
        id: user.id,
      });

      return {
        success: true,
        jwt,
        user,
        message: "Login successful",
      };
    } catch (error) {
      console.error("Error in callback:", error);
      throw error;
    }
  },

  /**
   * Update current user profile
   */
  async updateMe(userId: number, data: any) {
    try {
      // Pick only allowed fields for security
      const allowedFields = [
        "name",
        "phone",
        "email",
        "address",
        "province",
        "district",
        "ward",
        "avatarUrl",
        "dob",
        "taxCode",
        "bankInfo",
        "cccd",
      ];

      const updateData: any = {};
      for (const field of allowedFields) {
        if (data[field] !== undefined) {
          updateData[field] = data[field];
        }
      }

      // Update user
      const updatedUser = await strapi.db
        .query("plugin::users-permissions.user")
        .update({
          where: { id: userId },
          data: updateData,
          populate: {
            role: true,
          },
        });

      return {
        success: true,
        user: updatedUser,
        message: "Profile updated successfully",
      };
    } catch (error) {
      console.error("Error in updateMe:", error);
      throw error;
    }
  },
});

export default zaloMiniAppService;
